<script lang="ts">
  import { createEventDispatcher, onMount } from "svelte";
  import type { GhostPost } from "../types";
  import { getPluginContext } from "./context";
  import { ObsidianGhostAPI } from "../api/ghost-api";
  import { Notice } from "obsidian";

  export let show: boolean = false;

  const { plugin } = getPluginContext();
  const dispatch = createEventDispatcher<{
    select: GhostPost;
    cancel: void;
  }>();

  let posts: GhostPost[] = [];
  let filteredPosts: GhostPost[] = [];
  let searchQuery: string = "";
  let loading: boolean = false;
  let error: string = "";

  $: {
    if (searchQuery.trim() === "") {
      filteredPosts = posts;
    } else {
      const query = searchQuery.toLowerCase();
      filteredPosts = posts.filter(
        (post) =>
          post.title.toLowerCase().includes(query) ||
          post.slug.toLowerCase().includes(query),
      );
    }
  }

  async function loadPosts() {
    if (!plugin.settings.ghostAdminApiKey) {
      error =
        "Ghost Admin API key not configured. Please check plugin settings.";
      return;
    }

    loading = true;
    error = "";

    try {
      const ghostAPI = new ObsidianGhostAPI(
        plugin.settings.ghostUrl,
        plugin.settings.ghostAdminApiKey,
      );
      posts = await ghostAPI.getAllPostsWithContent();

      if (posts.length === 0) {
        error = "No posts found in Ghost";
      }
    } catch (err) {
      error = `Error loading posts: ${err.message}`;
      console.error("Error loading posts:", err);
    } finally {
      loading = false;
    }
  }

  function handleSelect(post: GhostPost) {
    dispatch("select", post);
    show = false;
  }

  function handleCancel() {
    dispatch("cancel");
    show = false;
  }

  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      handleCancel();
    }
  }

  function handleBackdropKeydown(event: KeyboardEvent) {
    if (event.key === "Escape" || event.key === "Enter") {
      handleCancel();
    }
  }

  function formatDate(dateString: string | null): string {
    if (!dateString) return "Draft";
    return new Date(dateString).toLocaleDateString();
  }

  function formatTags(tags: any[]): string {
    if (!tags || tags.length === 0) return "No tags";
    return tags.map((t) => t.name).join(", ");
  }

  $: if (show && posts.length === 0 && !loading && !error) {
    loadPosts();
  }
</script>

{#if show}
  <div
    class="modal-backdrop"
    data-modal-type="post-browser"
    role="button"
    tabindex="0"
    on:click={handleBackdropClick}
    on:keydown={handleBackdropKeydown}
  >
    <div class="modal-content post-browser-modal">
      <div class="post-browser-header">
        <h2>Browse Ghost Posts</h2>
        <button class="close-btn" data-action="close" on:click={handleCancel}
          >×</button
        >
      </div>

      {#if loading}
        <div class="post-browser-loading">
          <p>Loading posts from Ghost...</p>
        </div>
      {:else if error}
        <div class="post-browser-error">
          <p>{error}</p>
          <button class="ghost-sync-btn" on:click={loadPosts}>Retry</button>
        </div>
      {:else}
        <div class="post-browser-search">
          <input
            type="text"
            bind:value={searchQuery}
            placeholder="Type to search posts..."
            class="post-browser-search-input"
            data-input="search-posts"
          />
        </div>

        <div class="post-browser-list">
          {#each filteredPosts as post}
            <div
              class="ghost-post-suggestion"
              data-post-slug={post.slug}
              data-action="select-post"
              role="button"
              on:click={() => handleSelect(post)}
              on:keydown={(e) => e.key === "Enter" && handleSelect(post)}
              tabindex="0"
            >
              <div class="ghost-post-title">{post.title}</div>
              <div class="ghost-post-meta">
                <span class="post-status">
                  {post.status === "published" ? "📄" : "📝"}
                </span>
                <span class="post-date">{formatDate(post.published_at)}</span>
                <span class="post-separator">•</span>
                <span class="post-tags">{formatTags(post.tags)}</span>
              </div>
            </div>
          {/each}

          {#if filteredPosts.length === 0 && searchQuery.trim() !== ""}
            <div class="post-browser-no-results">
              <p>No posts found matching "{searchQuery}"</p>
            </div>
          {/if}
        </div>
      {/if}

      <div class="post-browser-footer">
        <button class="ghost-sync-btn" on:click={handleCancel}> Cancel </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .post-browser-modal {
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }

  .post-browser-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--background-modifier-border);
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn:hover {
    color: var(--text-normal);
  }

  .post-browser-loading,
  .post-browser-error,
  .post-browser-no-results {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
  }

  .post-browser-search {
    margin-bottom: 16px;
  }

  .post-browser-search-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-size: 14px;
  }

  .post-browser-list {
    flex: 1;
    overflow-y: auto;
    max-height: 400px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
  }

  .ghost-post-suggestion {
    padding: 12px 16px;
    border-bottom: 1px solid var(--background-modifier-border);
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .ghost-post-suggestion:last-child {
    border-bottom: none;
  }

  .ghost-post-suggestion:hover,
  .ghost-post-suggestion:focus {
    background: var(--background-modifier-hover);
    outline: none;
  }

  .ghost-post-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-normal);
  }

  .ghost-post-meta {
    font-size: 12px;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .post-separator {
    opacity: 0.5;
  }

  .post-browser-footer {
    margin-top: 20px;
    padding-top: 12px;
    border-top: 1px solid var(--background-modifier-border);
    display: flex;
    justify-content: flex-end;
  }
</style>
